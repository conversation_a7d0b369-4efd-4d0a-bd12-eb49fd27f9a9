import React from 'react';
import { View, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GifAvatar from '../avatar/GifAvatar';
import AndroidGifAvatar from '../avatar/AndroidGifAvatar';

interface AvatarData {
  type: 'custom' | 'icon';
  value: string;
  color?: string;
  imagePath?: string;
}

interface UserAvatarProps {
  avatarData?: string; // JSON string containing avatar data
  avatar?: string; // Fallback simple avatar identifier
  size?: number;
  backgroundColor?: string;
  style?: any;
}

// Avatar mapping for custom avatars
const customAvatarMap: { [key: string]: any } = {
  'Pirate': require('../../../assets/avatars/avatar_pirate.gif'),
  'Cat': require('../../../assets/avatars/avatar_cat.gif'),
  'Dog': require('../../../assets/avatars/avatar_dog.gif'),
  'Laugh': require('../../../assets/avatars/avatar_laugh.gif'),
  'Ninja': require('../../../assets/avatars/avatar_ninja.gif'),
  'Sad': require('../../../assets/avatars/avatar_sad.gif'),
  'Shocked': require('../../../assets/avatars/avatar_shocked.gif'),
  'Smile': require('../../../assets/avatars/avatar_smile.gif'),
  'Upset': require('../../../assets/avatars/avatar_upset.gif'),
  'Weird': require('../../../assets/avatars/avatar_weird.gif'),
  'Wizard': require('../../../assets/avatars/avatar_wizard.gif'),
};

// Default colors for icon avatars
const defaultIconColors: { [key: string]: string } = {
  'person': '#FF5733',
  'happy': '#33FF57',
  'rocket': '#3357FF',
  'planet': '#FF33E6',
  'star': '#FFD700',
  'heart': '#FF4081',
  'paw': '#795548',
  'football': '#8D6E63',
  'basketball': '#FF9800',
  'musical-notes': '#2196F3',
  'game-controller': '#673AB7',
  'brush': '#4CAF50',
  'flash': '#FFC107',
};

/**
 * UserAvatar component that handles both custom GIF avatars and icon avatars
 * Automatically determines the avatar type and renders appropriately
 */
const UserAvatar: React.FC<UserAvatarProps> = ({
  avatarData,
  avatar,
  size = 32,
  backgroundColor,
  style,
}) => {
  // Parse avatar data
  let parsedAvatarData: AvatarData | null = null;
  
  if (avatarData) {
    try {
      parsedAvatarData = JSON.parse(avatarData);
    } catch (error) {
      console.warn('Failed to parse avatar data:', error);
    }
  }

  // Determine avatar type and properties
  const isCustomAvatar = parsedAvatarData?.type === 'custom' && parsedAvatarData.imagePath;
  const avatarValue = parsedAvatarData?.value || avatar || 'person';
  const avatarColor = backgroundColor || parsedAvatarData?.color || defaultIconColors[avatarValue] || '#4361EE';

  // Render custom GIF avatar
  if (isCustomAvatar && parsedAvatarData?.imagePath) {
    const imageSource = customAvatarMap[parsedAvatarData.imagePath];
    
    if (imageSource) {
      return (
        <View style={[{ width: size, height: size, borderRadius: size / 2, overflow: 'hidden' }, style]}>
          {Platform.OS === 'android' ? (
            <AndroidGifAvatar
              source={imageSource}
              size={size}
            />
          ) : (
            <GifAvatar
              source={imageSource}
              size={size}
            />
          )}
        </View>
      );
    }
  }

  // Render icon avatar (fallback or default)
  return (
    <View
      style={[
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: avatarColor,
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}
    >
      <Ionicons
        name={avatarValue as any}
        size={size * 0.6}
        color="#FFFFFF"
      />
    </View>
  );
};

export default UserAvatar;
